@page "/task-groups/create"
@inherits TaskTrackingComponentBase
@using Severity = MudBlazor.Severity

<div class="islamic-pattern-bg">
    <MudContainer MaxWidth="MaxWidth.Large" Class="py-4">
        <!-- Header Section -->
        <MudGrid Class="mb-4" AlignItems="Center">
            <MudItem xs="12" sm="8" md="9">
                <div class="d-flex align-items-center">
                    <MudIconButton Icon="@Icons.Material.Filled.ArrowBack"
                                   Color="Color.Primary"
                                   OnClick="@(() => NavigationManager.NavigateTo("/"))"
                                   Class="me-3" />
                    <div>
                        <MudText Typo="Typo.h4" Class="section-title mb-1">
                            <MudIcon Icon="@Icons.Material.Filled.Add" Class="me-2" />
                            @L["CreateTaskGroup"]
                        </MudText>
                        <MudText Typo="Typo.body2" Class="text-muted d-none d-sm-block">
                            @L["CreateNewTaskGroupDetails"]
                        </MudText>
                    </div>
                </div>
            </MudItem>
            
            <!-- Action Buttons -->
            <MudItem xs="12" sm="4" md="3">
                <MudStack Row="true" Spacing="2" Class="d-none d-sm-flex justify-end">
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Groups"
                               OnClick="@(() => NavigationManager.NavigateTo("/task-groups/all"))">
                        @L["AllGroups"]
                    </MudButton>
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Secondary"
                               StartIcon="@Icons.Material.Filled.Person"
                               OnClick="@(() => NavigationManager.NavigateTo("/task-groups/my"))">
                        @L["MyGroups"]
                    </MudButton>
                </MudStack>
                
                <!-- Mobile Buttons -->
                <MudStack Spacing="2" Class="d-block d-sm-none">
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Groups"
                               OnClick="@(() => NavigationManager.NavigateTo("/task-groups/all"))"
                               FullWidth="true">
                        @L["AllGroups"]
                    </MudButton>
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Secondary"
                               StartIcon="@Icons.Material.Filled.Person"
                               OnClick="@(() => NavigationManager.NavigateTo("/task-groups/my"))"
                               FullWidth="true">
                        @L["MyGroups"]
                    </MudButton>
                </MudStack>
            </MudItem>
        </MudGrid>

        <!-- Create Form -->
        <MudCard Class="islamic-card" Elevation="3">
            <MudCardContent Class="pa-6">
                <MudForm Model="@CreateDto" @ref="@form" Validation="@(CreateTaskGroupDtoValidator.ValidateValue)" ValidationDelay="0">
                    <!-- Title Field -->
                    <MudTextField @bind-Value="CreateDto.Title"
                                  Label="@L["Title"]"
                                  Variant="Variant.Outlined"
                                  Class="mb-4"
                                  Required="true"
                                  MaxLength="200"
                                  Counter="200"
                                  HelperText="@L["EnterTaskGroupTitle"]"
                                  For="@(() => CreateDto.Title)"
                                  Immediate="true" />

                    <!-- Description Field -->
                    <MudGrid Class="mb-4" Spacing="10">
                        <MudItem xs="12" md="6">
                            <MudTextField @bind-Value="CreateDto.Description"
                                          Label="@L["Description"]"
                                          Variant="Variant.Outlined"
                                          Lines="8"
                                          Class="mb-4"
                                          Required="true"
                                          MaxLength="4000"
                                          Counter="4000"
                                          HelperText="@L["EnterTaskGroupDescription"]"
                                          Immediate="true"
                                          For="@(() => CreateDto.Description)"/>
                        </MudItem>

                        <MudItem xs="12" md="6" Style="margin-top: 20px">
                            <div class="markdown-content">
                                <MudMarkdown Value="@CreateDto.Description"/>
                            </div>
                        </MudItem>
                    </MudGrid>

                    <!-- Date Fields -->
                    <MudGrid Class="mb-4">
                        <MudItem xs="12" md="6">
                            <MudDatePicker @bind-Date="StartDatePicker"
                                           Label="@L["StartDate"]"
                                           Variant="Variant.Outlined"
                                           Required="true"
                                           DateFormat="dd/MM/yyyy"
                                           HelperText="@L["SelectStartDate"]"
                                           For="@(() => CreateDto.StartDate)" />
                        </MudItem>
                        <MudItem xs="12" md="6">
                            <MudDatePicker @bind-Date="EndDatePicker"
                                           Label="@L["EndDate"]"
                                           Variant="Variant.Outlined"
                                           DateFormat="dd/MM/yyyy"
                                           Clearable="true"
                                           HelperText="@L["OptionalEndDate"]"
                                           For="@(() => CreateDto.EndDate)" />
                        </MudItem>
                    </MudGrid>

                    <!-- Information Card -->
                    <MudAlert Severity="Severity.Info" Class="mb-4">
                        <MudText Typo="Typo.body2">
                            <MudIcon Icon="@Icons.Material.Filled.Info" Size="Size.Small" Class="me-2" />
                            @L["CreateTaskGroupInfo"]
                        </MudText>
                    </MudAlert>

                    <!-- Action Buttons -->
                    <MudStack Row="true" Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="d-none d-sm-flex">
                        <MudButton Variant="Variant.Text"
                                   Color="Color.Default"
                                   StartIcon="@Icons.Material.Filled.Cancel"
                                   OnClick="@(() => NavigationManager.NavigateTo("/"))">
                            @L["Cancel"]
                        </MudButton>

                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Primary"
                                   StartIcon="@Icons.Material.Filled.Save"
                                   OnClick="@(async () => await Submit())"
                                   Disabled="@IsSubmitting">
                            @if (IsSubmitting)
                            {
                                <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="me-2" />
                            }
                            @L["CreateTaskGroup"]
                        </MudButton>
                    </MudStack>

                    <!-- Mobile Action Buttons -->
                    <MudStack Spacing="2" Class="d-block d-sm-none">
                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Primary"
                                   StartIcon="@Icons.Material.Filled.Save"
                                   OnClick="@(async () => await Submit())"
                                   Disabled="@IsSubmitting"
                                   FullWidth="true">
                            @if (IsSubmitting)
                            {
                                <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="me-2" />
                            }
                            @L["CreateTaskGroup"]
                        </MudButton>
                        <MudButton Variant="Variant.Text"
                                   Color="Color.Default"
                                   StartIcon="@Icons.Material.Filled.Cancel"
                                   OnClick="@(() => NavigationManager.NavigateTo("/"))"
                                   FullWidth="true">
                            @L["Cancel"]
                        </MudButton>
                    </MudStack>
                </MudForm>
            </MudCardContent>
        </MudCard>

        <!-- Quick Start Tips -->
        <MudCard Class="mt-4" Elevation="2">
            <MudCardContent Class="pa-4">
                <MudText Typo="Typo.h6" Class="mb-3">
                    <MudIcon Icon="@Icons.Material.Filled.Lightbulb" Class="me-2" Color="Color.Warning" />
                    @L["QuickStartTips"]
                </MudText>
                <MudList T="string" Dense="true">
                    <MudListItem T="string" Icon="@Icons.Material.Filled.CheckCircle" IconColor="Color.Success">
                        <MudText Typo="Typo.body2">@L["TipChooseDescriptiveTitle"]</MudText>
                    </MudListItem>
                    <MudListItem T="string" Icon="@Icons.Material.Filled.CheckCircle" IconColor="Color.Success">
                        <MudText Typo="Typo.body2">@L["TipAddDetailedDescription"]</MudText>
                    </MudListItem>
                    <MudListItem T="string" Icon="@Icons.Material.Filled.CheckCircle" IconColor="Color.Success">
                        <MudText Typo="Typo.body2">@L["TipSetRealisticDates"]</MudText>
                    </MudListItem>
                    <MudListItem T="string" Icon="@Icons.Material.Filled.CheckCircle" IconColor="Color.Success">
                        <MudText Typo="Typo.body2">@L["TipEndDateOptional"]</MudText>
                    </MudListItem>
                </MudList>
            </MudCardContent>
        </MudCard>
    </MudContainer>
</div>