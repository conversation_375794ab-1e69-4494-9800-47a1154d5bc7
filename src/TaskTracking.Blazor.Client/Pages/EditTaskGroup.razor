@page "/task-groups/{id:guid}/edit"
@inherits TaskTrackingComponentBase

<div class="islamic-pattern-bg">
    <MudContainer MaxWidth="MaxWidth.Large" Class="py-4">
        <!-- Header Section -->
        <MudGrid Class="mb-4" AlignItems="Center">
            <MudItem xs="12" sm="8" md="9">
                <div class="d-flex align-items-center">
                    <MudIconButton Icon="@Icons.Material.Filled.ArrowBack"
                                   Color="Color.Primary"
                                   OnClick="@(() => NavigationManager.NavigateTo("/"))"
                                   Class="me-3" />
                    <div>
                        <MudText Typo="Typo.h4" Class="section-title mb-1">
                            <MudIcon Icon="@Icons.Material.Filled.Edit" Class="me-2" />
                            @L["EditTaskGroup"]
                        </MudText>
                        <MudText Typo="Typo.body2" Class="text-muted d-none d-sm-block">
                            @L["UpdateTaskGroupDetails"]
                        </MudText>
                    </div>
                </div>
            </MudItem>
            
            <!-- Action Buttons -->
            <MudItem xs="12" sm="4" md="3">
                <MudStack Row="true" Spacing="2" Class="d-none d-sm-flex justify-end">
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Groups"
                               OnClick="@(() => NavigationManager.NavigateTo("/task-groups/all"))">
                        @L["AllGroups"]
                    </MudButton>
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Secondary"
                               StartIcon="@Icons.Material.Filled.Person"
                               OnClick="@(() => NavigationManager.NavigateTo("/task-groups/my"))">
                        @L["MyGroups"]
                    </MudButton>
                </MudStack>
                
                <!-- Mobile Buttons -->
                <MudStack Spacing="2" Class="d-block d-sm-none">
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Groups"
                               OnClick="@(() => NavigationManager.NavigateTo("/task-groups/all"))"
                               FullWidth="true">
                        @L["AllGroups"]
                    </MudButton>
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Secondary"
                               StartIcon="@Icons.Material.Filled.Person"
                               OnClick="@(() => NavigationManager.NavigateTo("/task-groups/my"))"
                               FullWidth="true">
                        @L["MyGroups"]
                    </MudButton>
                </MudStack>
            </MudItem>
        </MudGrid>

        @if (IsLoading)
        {
            <div class="text-center py-5">
                <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
                <MudText Typo="Typo.body1" Class="mt-3">@L["LoadingTaskGroup"]</MudText>
            </div>
        }
        else if (TaskGroup == null)
        {
            <MudAlert Severity="Severity.Error" Class="mb-4">
                <MudText>@L["TaskGroupNotFound"]</MudText>
            </MudAlert>
        }
        else
        {
            <!-- Edit Form -->
            <MudCard Class="islamic-card" Elevation="3">
                <MudCardContent Class="pa-6">
                    <MudForm Model="@UpdateDto" @ref="@form" Validation="@(UpdateTaskGroupDtoValidator.ValidateValue)" ValidationDelay="0">
                        <!-- Title Field -->
                        <MudTextField @bind-Value="UpdateDto.Title"
                                      Label="@L["Title"]"
                                      Variant="Variant.Outlined"
                                      Class="mb-4"
                                      Required="true"
                                      MaxLength="200"
                                      Counter="200"
                                      HelperText="@L["EnterTaskGroupTitle"]"
                                      For="@(() => UpdateDto.Title)"
                                      Immediate="true" />

                        <!-- Description Field -->
                        <MudGrid Class="mb-4" Spacing="10">
                            <MudItem xs="12" md="6">
                                <MudTextField @bind-Value="UpdateDto.Description"
                                              Label="@L["Description"]"
                                              Variant="Variant.Outlined"
                                              Lines="8"
                                              Class="mb-4"
                                              Required="true"
                                              MaxLength="4000"
                                              Counter="4000"
                                              HelperText="@L["EnterTaskGroupDescription"]"
                                              Immediate="true"
                                              For="@(() => UpdateDto.Description)"/>
                            </MudItem>

                            <MudItem xs="12" md="6" Style="margin-top: 20px">
                                <div class="markdown-content">
                                    <MudMarkdown Value="@UpdateDto.Description"/>
                                </div>
                            </MudItem>
                        </MudGrid>

                        <!-- Date Fields -->
                        <MudGrid Class="mb-4">
                            <MudItem xs="12" md="6">
                                <MudDatePicker @bind-Date="StartDatePicker"
                                               Label="@L["StartDate"]"
                                               Variant="Variant.Outlined"
                                               Required="true"
                                               DateFormat="dd/MM/yyyy"
                                               For="@(() => UpdateDto.StartDate)" />
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudDatePicker @bind-Date="EndDatePicker"
                                               Label="@L["EndDate"]"
                                               Variant="Variant.Outlined"
                                               DateFormat="dd/MM/yyyy"
                                               Clearable="true"
                                               HelperText="@L["OptionalEndDate"]"
                                               For="@(() => UpdateDto.EndDate)" />
                            </MudItem>
                        </MudGrid>

                        <!-- Action Buttons -->
                        <MudStack Row="true" Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="d-none d-sm-flex">
                            <MudButton Variant="Variant.Text"
                                       Color="Color.Default"
                                       StartIcon="@Icons.Material.Filled.Cancel"
                                       OnClick="@(() => NavigationManager.NavigateTo("/"))">
                                @L["Cancel"]
                            </MudButton>

                            <MudButton OnClick="@HandleValidSubmit"
                                       Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       StartIcon="@Icons.Material.Filled.Save"
                                       Disabled="@IsSubmitting">
                                @if (IsSubmitting)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="me-2" />
                                }
                                @L["SaveChanges"]
                            </MudButton>
                        </MudStack>

                        <!-- Mobile Action Buttons -->
                        <MudStack Spacing="2" Class="d-block d-sm-none">
                            <MudButton OnClick="@HandleValidSubmit"
                                       Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       StartIcon="@Icons.Material.Filled.Save"
                                       Disabled="@IsSubmitting"
                                       FullWidth="true">
                                @if (IsSubmitting)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="me-2" />
                                }
                                @L["SaveChanges"]
                            </MudButton>
                            <MudButton Variant="Variant.Text"
                                       Color="Color.Default"
                                       StartIcon="@Icons.Material.Filled.Cancel"
                                       OnClick="@(() => NavigationManager.NavigateTo("/"))"
                                       FullWidth="true">
                                @L["Cancel"]
                            </MudButton>
                        </MudStack>
                    </MudForm>
                </MudCardContent>
            </MudCard>
        }
    </MudContainer>
</div>